<script lang="ts" setup>
import type { PurpleAPIModel } from '@/api/api.model'

export interface EvaluateListItemProps {
  item: PurpleAPIModel
}

defineProps<EvaluateListItemProps>()
</script>

<template>
  <div class="evaluate-item">
    <div class="evaluate-header">
      <div class="name-container">
        <div class="name">
          {{ item.name }}
        </div>
      </div>
      <div class="action">
        <span class="action-text">{{ item.linkText }}</span>
        <van-icon name="arrow" class="action-icon" />
      </div>
    </div>
    <div class="evaluate-info">
      <div class="info-row">
        <span class="label">评定时间</span>
        <span class="value">{{ item.lastUpdateTime }}</span>
      </div>
      <div class="info-row">
        <span class="label">评定对象</span>
        <span class="value">{{ item.assessorName }}</span>
        <div class="flex-1 truncate">
          <ColorTag color="gray" v-if="item.assessorOrgNames?.length" class="org-tag">
            {{ item.assessorOrgNames.join('/') }}
          </ColorTag>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.evaluate-item {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 16px 0;
  position: relative;
  width: 100%;

  border-bottom: 0.5px solid #ddd;

  &:first-child {
    padding-top: 0;
  }

  .evaluate-header {
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .name-container {
      display: flex;
      flex-direction: row;
      gap: 4px;
      align-items: center;

      .name {
        font-size: 14px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.9);
      }
    }

    .action {
      display: flex;
      flex-direction: row;
      gap: 2px;
      align-items: center;
      color: #999999;

      .action-text {
        font-size: 11px;
      }

      .action-icon {
        font-size: 11px;
      }
    }
  }

  .evaluate-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
    width: 100%;

    .info-row {
      display: flex;
      align-items: center;
      gap: 4px;

      .label {
        font-size: 12px;
        color: #666666;
      }

      .value {
        font-size: 12px;
        color: #666666;
        font-weight: bold;
      }
    }

    .org-tag {
      flex: 1;
      max-width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
