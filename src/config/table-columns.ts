interface TableColumn {
  title: string
  key: string
  align?: 'left' | 'center' | 'right'
  width?: string
  sortable?: boolean
  customRender?: (opt: { value: any, record: any }) => string | undefined
  search?: {
    field: string
    component: string
    componentProps?: any
  }
}
/**
 * 技能状态 PASS_RATE_NO:合格率不达标 EFFICIENCY_NOT:效率不达标 TRAIN_FREQUENCY_NOT:训练次数不达标
 * TRAIN_DURATION_NOT 时长不达标 PASS_OK:合格
 */
export const SKILL_STATUS = [
  {
    label: '合格',
    value: 'PASS_OK',
  },
  {
    label: '合格率不达标',
    value: 'PASS_RATE_NOT',
  },
  {
    label: '动作达标率不达标',
    value: 'ACTION_RATE_NOT',
  },
  {
    label: '效率不达标',
    value: 'EFFICIENCY_NOT',
  },
  {
    label: '训练次数不达标',
    value: 'TRAIN_FREQUENCY_NOT',
  },
  {
    label: '训练时长不达标',
    value: 'TRAIN_DURATION_NOT',
  },
]

// 训练报告列配置
export const trainingColumns: TableColumn[] = [
  {
    title: '项目名称',
    key: 'projectName',
    align: 'left',
    sortable: true,
  },
  {
    title: '用户名',
    key: 'userName',
    align: 'left',
    width: '120px',
    sortable: true,
  },
  {
    title: '要求时长(min)',
    key: 'requestDuration',
    align: 'center',
    width: '120px',
  },
  {
    title: '训练时长(min)',
    key: 'trainDuration',
    align: 'center',
    width: '120px',
  },
  {
    title: '要求次数',
    key: 'requestFrequency',
    align: 'center',
    width: '100px',
  },
  {
    title: '作业次数',
    key: 'opNum',
    align: 'center',
    width: '100px',
  },
  {
    title: '合格率要求',
    key: 'requestQualificationRate',
    align: 'center',
    width: '100px',
  },
  {
    title: '训练合格率',
    key: 'trainingQualifiedRate',
    align: 'center',
    width: '100px',
  },
  {
    title: '动作达标率要求',
    key: 'requestActionRate',
    align: 'center',
    width: '120px',
  },
  {
    title: '动作达标率',
    key: 'actionRate',
    align: 'center',
    width: '100px',
  },
  {
    title: '效率要求(次/min)',
    key: 'requestEfficiency',
    align: 'center',
    width: '140px',
  },
  {
    title: '最近效率(次/min)',
    key: 'lastEfficiency',
    align: 'center',
    width: '140px',
  },
  {
    title: '训练进度',
    key: 'statusShow',
    align: 'center',
    width: '100px',
  },
  {
    title: '技能状态',
    key: 'skillText',
    align: 'center',
    width: '120px',
  },
  {
    title: '最近更新时间',
    key: 'lastUpdateTime',
    align: 'center',
    width: '160px',
  },
]

// 考试报告列配置
export const examColumns: TableColumn[] = [
  {
    title: '项目名称',
    key: 'projectName',
    align: 'left',
    sortable: true,
  },
  {
    title: '用户名',
    key: 'userName',
    align: 'left',
    width: '120px',
    sortable: true,
  },
  {
    title: '考核时长(min)',
    key: 'requestDuration',
    align: 'center',
    width: '120px',
  },
  {
    title: '要求次数',
    key: 'requestFrequency',
    align: 'center',
    width: '100px',
  },
  {
    title: '合格率要求',
    key: 'requestQualificationRate',
    align: 'center',
    width: '100px',
  },
  {
    title: '动作达标率要求',
    key: 'requestActionRate',
    align: 'center',
    width: '120px',
  },
  {
    title: '实操考核时长(min)',
    key: 'trainDurationText',
    align: 'center',
    width: '140px',
  },
  {
    title: '考核合格率',
    key: 'passRate',
    align: 'center',
    width: '100px',
  },
  {
    title: '考核达标率',
    key: 'actionPassRate',
    align: 'center',
    width: '100px',
  },
  {
    title: '考核状态',
    key: 'statusShow',
    align: 'center',
    width: '120px',
  },
]

// 比赛报告列配置
export const competitionColumns: TableColumn[] = [
  {
    title: '项目名称',
    key: 'projectName',
    align: 'left',
    sortable: true,
  },
  {
    title: '用户名',
    key: 'userName',
    align: 'left',
    width: '120px',
    sortable: true,
  },
  {
    title: '限制时长(min)',
    key: 'requestDuration',
    align: 'center',
    width: '120px',
  },
  {
    title: '有效作业次数',
    key: 'effectiveNum',
    align: 'center',
    width: '110px',
  },
  {
    title: '要求次数',
    key: 'requestFrequency',
    align: 'center',
    width: '100px',
  },
  {
    title: '实际用时(min)',
    key: 'trainDuration',
    align: 'center',
    width: '120px',
  },
  {
    title: '竞赛排名',
    key: 'ranking',
    align: 'center',
    width: '120px',
  },
]
enum DictColorTypeEnum {
  PRIMARY = 'primary',
  ERROR = 'error',
  WARNING = 'warning',
  SUCCESS = 'success',
  DISABLED = 'disabled',
}
interface DictItem {
  value: number | string
  label: number | string
  colorType?: DictColorTypeEnum
}
/**
 * 动作类别：
 * 1: 未双手作业、2: 未垂直作业面、3: 拧紧未贴合、4: 拧紧枪未亮绿灯
 */
export const ACTION_TYPES_WRONG: DictItem[] = [
  {
    label: '未双手作业',
    value: 1,
  },
  {
    label: '未垂直作业面',
    value: 2,
  },
  {
    label: '拧紧未贴合',
    value: 3,
  },
  {
    label: '拧紧枪未亮绿灯',
    value: 4,
  },
  {
    label: '未戴手套',
    value: 8,
  },
  {
    label: '堵盖选择错误',
    value: 21,
  },
  {
    label: '堵盖不贴合',
    value: 22,
  },
  {
    label: '堵盖未按压',
    value: 23,
  },
  {
    label: '安装不规范',
    value: 31,
  },
  {
    label: '按压不规范',
    value: 32,
  },
  {
    label: '安装不贴合',
    value: 33,
  },
  {
    label: '油管未闭合',
    value: 41,
  },
  {
    label: '插拔管未闭合',
    value: 42,
  },
  {
    label: '安装完成未回拔',
    value: 43,
  },
  {
    label: '安装完成未回拔',
    value: 51,
  },
  {
    label: '安装不规范',
    value: 52,
  },
]
