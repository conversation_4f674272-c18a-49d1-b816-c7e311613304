import { cloneDeep } from 'lodash-es'
import { watch } from 'vue'
import { useRouter } from 'vue-router'

export function useRouteQuery<T extends Record<string, any>>() {
  const router = useRouter()
  const currentRoute = router.currentRoute.value
  const urlParams = reactive(cloneDeep(currentRoute.query))

  watch(
    () => urlParams,
    () => {
      router.replace({
        path: router.currentRoute.value.path,
        query: urlParams,
      })
    },
    { deep: true, flush: 'post' },
  )

  return urlParams as UseRouteQueryResult<T>
}

export type UseRouteQueryResult<T extends Record<string, any>> = {
  [key in keyof T]: T[key] extends Array<infer U> ? U | Array<U> : T[key]
}
