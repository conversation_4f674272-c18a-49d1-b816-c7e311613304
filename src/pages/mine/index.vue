<script lang="ts" setup>
import dayjs from 'dayjs'
import { V1LocationHomeMine, V1LocationHomeUserDataUserId } from '@/api/api.req'
import Footer from '@/components/layOut/footer.vue'
import QrScanner from '@/components/QrScanner/index.vue'
import { TrainingProjectType, TrainingStatus } from '@/enums/training'
import { useAsyncData } from '@/hooks/useAsyncData'
import { gucStore } from '@/integration/guc'
import { currentUserInfo } from '@/store/sysUser'
import { EvaluateTypeQuery } from '../evaluate/index.vue'
import { toArray } from '@/utils'

const router = useRouter()

const basicInfoApi = useAsyncData(
  () => V1LocationHomeUserDataUserId({ userId: currentUserInfo.value.id! }),
  {},
)

const detailDataApi = useAsyncData(V1LocationHomeMine, {})

fetchInitData()

function fetchInitData() {
  basicInfoApi.load()
  detailDataApi.load()
}

async function loginOut() {
  await gucStore.logoutConfirm()
}

function formatDuration(duration = 0) {
  const d = dayjs.duration(duration, 's')
  const hours = ~~d.asHours()
  const minutes = ~~d.add(-hours, 'h').asMinutes()
  return ` ${hours} 时 ${minutes} 分`
}

const detailListData = computed(() => {
  const d = detailDataApi.data.value
  return [
    {
      title: '学习',
      links: [
        {
          title: [
            //
            `已学习${formatDuration(+d.learningDuration!)}`,
            `${d.totalLearningTimes || 0} 次`,
          ],
          linkText: `查看记录`,
          onClick() {
            router.push({
              path: '/learning/list',
            })
          },
        },
        {
          title: `剩余 ${d.remainLearningNum || 0} 条学习任务`,
          alert: true,
          linkText: `查看任务`,
          onClick() {
            router.push('/learning')
          },
        },
      ],
    },
    {
      title: '技能训练',
      links: [
        {
          title: [
            //
            `已训练${formatDuration(+d.trainDuration!)}`,
            `${d.trainingNum || 0} 次`,
          ],
          linkText: `查看记录`,
          onClick() {
            router.push({
              path: '/training',
              query: {
                type: TrainingProjectType.Training,
                status: [
                  TrainingStatus.TO_BE_CHECK,
                  TrainingStatus.COMPLETED,
                  TrainingStatus.CHECKED,
                  TrainingStatus.ING,
                ],
              },
            })
          },
        },
        {
          title: `剩余 ${d.remainTrainNum || 0} 条训练任务`,
          linkText: `查看任务`,
          alert: true,
          onClick() {
            router.push({
              path: '/training',
              query: {
                type: TrainingProjectType.Training,
                status: [TrainingStatus.ING, TrainingStatus.TO_BE],
              },
            })
          },
        },
      ],
    },
    {
      title: '技能考核',
      links: [
        {
          title: `已考核 ${d.checkNum || 0} 次`,
          linkText: `查看记录`,
          onClick() {
            router.push({
              path: '/training',
              query: {
                type: TrainingProjectType.Exam,
                isCheck: 1,
                status: [TrainingStatus.FAILED_CHECK, TrainingStatus.PASSED_CHECK],
              },
            })
          },
        },
        {
          title: `剩余 ${d.remainCheckNum || 0} 条考核任务`,
          linkText: `查看任务`,
          alert: true,
          onClick() {
            router.push({
              path: '/training',
              query: {
                title: '考核列表',
                isCheck: 1,
                type: TrainingProjectType.Training,
                status: [TrainingStatus.TO_BE_CHECK],
              },
            })
          },
        },
      ],
    },
    {
      title: '技能竞赛',
      links: [
        {
          title: `已竞赛 ${d.raceNum || 0} 次`,
          linkText: `查看记录`,
          onClick() {
            router.push({
              path: '/training',
              query: {
                type: TrainingProjectType.Race,
              },
            })
          },
        },
      ],
    },
    {
      title: '评定',
      links: [
        {
          title: `评定他人 ${d.evaluateNum || 0} 次`,
          linkText: `查看记录`,
          onClick() {
            router.push({
              path: '/evaluate',
              query: {
                type: EvaluateTypeQuery.Reviewer,
              },
            })
          },
        },
        {
          title: `被评定 ${d.assessedNum || 0} 次`,
          linkText: `查看记录`,
          onClick() {
            router.push({
              path: '/evaluate',
            })
          },
        },
      ],
    },
  ]
})
</script>

<template>
  <VanNavBar title="我的"> </VanNavBar>
  <div class="page-content page-content-padding">
    <CardBox class="p-0!">
      <div class="avatar-info flex pt-4">
        <div class="avatar mx-4 mt-3">
          <img class="size-full" src="@/assets/guc-avatar.svg" />
        </div>
        <div class="flex flex-col">
          <div class="mb-2 flex items-center gap-1">
            <span class="name font-bold">
              {{ currentUserInfo.name }}
            </span>
            <ColorTag color="gray">
              {{ currentUserInfo.jobName }} L{{ currentUserInfo.level }}
            </ColorTag>
          </div>
          <div class="flex gap-1">
            <div class="level-tag text-12px" v-if="false">
              <SvgIcon name="svg/item/discount" size="1em" />
              <span> L{{ basicInfoApi.data.value.level || 0 }} </span>
            </div>
            <ColorTag color="green" shape="round" border>
              {{ basicInfoApi.data.value.orgList?.map((n) => n.name).join('/') }}
            </ColorTag>
          </div>
          <div class="my-5">
            <QrScanner
              button-text="扫一扫"
              button-type="primary"
              button-size="small"
              button-class=""
            />
          </div>
        </div>
      </div>
    </CardBox>

    <CardBox class="mt-4 flex flex-col gap-2 text-sm">
      <div v-for="item in detailListData" class="detail-item">
        <div class="font-bold">
          {{ item.title }}
        </div>

        <template v-for="link in item.links">
          <div class="detail-item-alert" v-if="link.alert">
            <div class="icon">
              <SvgIcon name="svg/notification-filled" />
            </div>
            <div class="flex-1 truncate">
              {{ link.title }}
            </div>
            <VanButton type="primary" size="small" @click="link.onClick">
              {{ link.linkText }}
            </VanButton>
          </div>
          <div class="detail-item-link flex" v-else>
            <div class="item-title flex-1 truncate text-#8B8B8B">
              <span class="item-label" v-for="label in toArray(link.title)">
                {{ label }}
              </span>
            </div>
            <div class="link" @click="link.onClick">
              <span class="text-#242424">
                {{ link.linkText }}
              </span>
              <VanIcon name="arrow" color="#A6A6A6" />
            </div>
          </div>
        </template>
      </div>
    </CardBox>

    <div class="logout-btn" @click="loginOut">退出当前账号</div>
  </div>
  <Footer />
</template>

<style lang="less" scoped>
.avatar {
  @size: 64px;
  width: @size;
  height: @size;
  border-radius: 999px;
}

.avatar-info {
  background:
    linear-gradient(339deg, rgba(255, 255, 255, 0) 68.39%, #fff 82.74%),
    linear-gradient(278deg, rgba(255, 255, 255, 0) 8.95%, #fff 98.4%),
    url('@/assets/home-bg.jpg') lightgray left -40px / 100% auto no-repeat;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item-link {
  border-radius: 2px;
  background: var(--Gray-Gray2, #eee);
  display: flex;
  padding: 2px 8px;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;

  .item-title {
    .item-label {
      border-right: 1px solid #abd6c9;
      padding-right: 8px;
      margin-right: 8px;

      &:last-child {
        border-right: 0;
        padding-right: 0;
        margin-right: 0;
      }
    }
  }
}

.detail-item-alert {
  display: flex;
  align-items: center;

  .icon {
    color: #c5c5c5;
    display: flex;
  }
}

.logout-btn {
  padding: 5px 12px;

  margin-top: 12px;

  border-radius: 4px;
  background: var(--Gray-Gray3, #e7e7e7);

  color: var(--Gray-Gray8, #777);
  text-align: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
</style>
