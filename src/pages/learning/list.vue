<script lang="ts" setup>
import type { V1ManageUserLearningsLearningDetailIDGetResponseResult } from '@/api/api.model'
import { V1ManageUserLearningsPagePost } from '@/api/api.req'
import { LearningStatus } from '@/enums/learning'
import { useDynamicList } from '@/hooks/useDynamicList'
import { useLoading } from '@/hooks/useLoading'
import { toFixed } from '@/utils'
import dayjs from 'dayjs'

const router = useRouter()

const learningListState = useDynamicList({
  api: async (params) => {
    const resp = await V1ManageUserLearningsPagePost({
      ...params,
      data: {
        statusList: [LearningStatus.LEARNING, LearningStatus.FINISH],
      },
    })

    return {
      ...resp,
      records: resp.records?.map((item) => ({
        ...item,
        id: item.id!,
        name: item.name!,
        tag: `已学 ${toFixed(dayjs.duration(+item.duration! || 0, 's').asMinutes(), 2)} 分钟`,
        linkText: `查看详情`,
      })),
    }
  },
})

const fetchData = useLoading(() => {
  learningListState.reset()
  learningListState.load()
})

fetchData()

function onClickItem(item: V1ManageUserLearningsLearningDetailIDGetResponseResult) {
  router.push({
    path: '/learning/detail',
    query: {
      id: item.id,
    },
  })
}
</script>

<template>
  <VanNavBar title="学习列表" left-arrow left-text="返回" @click-left="$router.back()" />
  <div class="page-content page-content-padding">
    <VanPullRefresh
      :model-value="fetchData.isLoading"
      success-text="刷新成功!"
      @refresh="fetchData"
    >
      <CardBox>
        <CommonList :list-state="learningListState">
          <template #default="{ item }">
            <CommonDetailListItem
              :title="item.name"
              :tag="item.tag"
              :time="item.startAt"
              :linkText="item.linkText"
              @click="onClickItem(item)"
            />
          </template>
        </CommonList>
      </CardBox>
    </VanPullRefresh>
  </div>
  <!-- <Footer /> -->
</template>

<style lang="less" scoped></style>
